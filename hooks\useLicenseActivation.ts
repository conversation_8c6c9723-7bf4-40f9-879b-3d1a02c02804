import { useState, useEffect } from 'react';
import CryptoJS from 'crypto-js';

// ⚠️ IMPORTANTE: Debe coincidir con la clave en ActivationDialog.tsx
const SECRET_KEY = 'StrategyCreator22SecretKey2025';

interface LicenseState {
  isActivated: boolean;
  macAddress: string;
  activationKey: string;
  activatedAt: string;
  isElectron: boolean;
}

// Función para obtener la dirección MAC real de la PC
const getMacAddress = async (): Promise<string> => {
  try {
    if (window.electronAPI && window.electronAPI.isElectron) {
      // Usar la API de Electron para obtener la MAC real
      const macAddress = await window.electronAPI.getMacAddress();
      return macAddress;
    } else {
      // Fallback para navegadores web
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Fingerprint', 2, 2);
      }
      
      const fingerprint = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        canvas.toDataURL(),
        navigator.hardwareConcurrency || 'unknown',
        navigator.deviceMemory || 'unknown'
      ].join('|');
      
      // Generar un hash que simule una MAC address
      const hash = CryptoJS.SHA256(fingerprint).toString();
      // Formatear como MAC address (XX:XX:XX:XX:XX:XX)
      const macLike = hash.substring(0, 12).match(/.{2}/g)?.join(':').toUpperCase() || '00:00:00:00:00:00';
      
      return macLike;
    }
  } catch (error) {
    console.error('Error obteniendo dirección MAC:', error);
    return '00:00:00:00:00:00';
  }
};

// Función para generar la clave esperada
const generateExpectedKey = (macAddress: string): string => {
  const hmac = CryptoJS.HmacSHA256(macAddress, SECRET_KEY);
  return hmac.toString().substring(0, 12).toUpperCase();
};

// Función para validar la clave de activación
const validateActivationKey = (macAddress: string, inputKey: string): boolean => {
  const expectedKey = generateExpectedKey(macAddress);
  return inputKey.toUpperCase() === expectedKey;
};

export const useLicenseActivation = () => {
  const [licenseState, setLicenseState] = useState<LicenseState>({
    isActivated: false,
    macAddress: '',
    activationKey: '',
    activatedAt: '',
    isElectron: false
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkActivation = async () => {
      try {
        // Verificar si estamos en Electron
        const isElectron = window.electronAPI && window.electronAPI.isElectron;
        
        // Obtener MAC address actual
        const currentMac = await getMacAddress();
        
        // Verificar si hay una activación guardada
        const savedKey = localStorage.getItem('strategy_creator_activation_key');
        const savedMac = localStorage.getItem('strategy_creator_mac_address');
        const savedDate = localStorage.getItem('strategy_creator_activated_at');
        
        if (savedKey && savedMac && savedDate) {
          // Verificar que la MAC coincida (protección contra transferencia de licencia)
          if (savedMac === currentMac) {
            // Verificar que la clave siga siendo válida
            if (validateActivationKey(currentMac, savedKey)) {
              setLicenseState({
                isActivated: true,
                macAddress: currentMac,
                activationKey: savedKey,
                activatedAt: savedDate,
                isElectron
              });
            } else {
              // Clave inválida, limpiar datos
              clearActivation();
              setLicenseState({
                isActivated: false,
                macAddress: currentMac,
                activationKey: '',
                activatedAt: '',
                isElectron
              });
            }
          } else {
            // MAC diferente, limpiar datos (protección contra transferencia)
            clearActivation();
            setLicenseState({
              isActivated: false,
              macAddress: currentMac,
              activationKey: '',
              activatedAt: '',
              isElectron
            });
          }
        } else {
          // No hay activación guardada
          setLicenseState({
            isActivated: false,
            macAddress: currentMac,
            activationKey: '',
            activatedAt: '',
            isElectron
          });
        }
      } catch (error) {
        console.error('Error verificando activación:', error);
        setLicenseState({
          isActivated: false,
          macAddress: '00:00:00:00:00:00',
          activationKey: '',
          activatedAt: '',
          isElectron: false
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkActivation();
  }, []);

  const clearActivation = () => {
    localStorage.removeItem('strategy_creator_activation_key');
    localStorage.removeItem('strategy_creator_mac_address');
    localStorage.removeItem('strategy_creator_activated_at');
  };

  const handleActivationSuccess = async () => {
    // Recargar el estado después de una activación exitosa
    const currentMac = await getMacAddress();
    const savedKey = localStorage.getItem('strategy_creator_activation_key');
    const savedDate = localStorage.getItem('strategy_creator_activated_at');
    const isElectron = window.electronAPI && window.electronAPI.isElectron;
    
    if (savedKey && savedDate) {
      setLicenseState({
        isActivated: true,
        macAddress: currentMac,
        activationKey: savedKey,
        activatedAt: savedDate,
        isElectron
      });
    }
  };

  const handleDeactivation = () => {
    clearActivation();
    setLicenseState(prev => ({
      ...prev,
      isActivated: false,
      activationKey: '',
      activatedAt: ''
    }));
  };

  return {
    licenseState,
    isLoading,
    handleActivationSuccess,
    handleDeactivation
  };
};
