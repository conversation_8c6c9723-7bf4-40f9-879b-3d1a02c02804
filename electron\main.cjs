const { app, <PERSON><PERSON>erWindow, ipcMain } = require('electron');
const path = require('path');
const os = require('os');

// Función para obtener la dirección MAC real de la PC
function getRealMacAddress() {
    try {
        const networkInterfaces = os.networkInterfaces();
        
        // Buscar la interfaz de red principal (no loopback, no virtual)
        for (const interfaceName in networkInterfaces) {
            const interfaces = networkInterfaces[interfaceName];
            
            for (const iface of interfaces) {
                // Buscar interfaz IPv4, no interna, no loopback
                if (iface.family === 'IPv4' && !iface.internal && iface.mac !== '00:00:00:00:00:00') {
                    // Priorizar interfaces Ethernet y WiFi
                    const name = interfaceName.toLowerCase();
                    if (name.includes('ethernet') || name.includes('wi-fi') || name.includes('wlan') || name.includes('eth')) {
                        return iface.mac.toUpperCase();
                    }
                }
            }
        }
        
        // Si no encontramos una interfaz prioritaria, buscar cualquier interfaz válida
        for (const interfaceName in networkInterfaces) {
            const interfaces = networkInterfaces[interfaceName];
            
            for (const iface of interfaces) {
                if (iface.family === 'IPv4' && !iface.internal && iface.mac !== '00:00:00:00:00:00') {
                    return iface.mac.toUpperCase();
                }
            }
        }
        
        // Como último recurso, buscar cualquier MAC que no sea 00:00:00:00:00:00
        for (const interfaceName in networkInterfaces) {
            const interfaces = networkInterfaces[interfaceName];
            
            for (const iface of interfaces) {
                if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
                    return iface.mac.toUpperCase();
                }
            }
        }
        
        return '00:00:00:00:00:00'; // Fallback si no se encuentra nada
    } catch (error) {
        console.error('Error obteniendo MAC address:', error);
        return '00:00:00:00:00:00';
    }
}

function createWindow() {
    // Crear la ventana del navegador
    const mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            enableRemoteModule: false,
            preload: path.join(__dirname, 'preload.cjs')
        },
        icon: path.join(__dirname, '../icon.ico'), // Usar el icon.ico del proyecto
        title: 'Strategy Creator 2.2',
        show: false, // No mostrar hasta que esté listo
        autoHideMenuBar: true, // Ocultar barra de menú por defecto
    });

    // Mostrar ventana cuando esté lista
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // Cargar la aplicación
    if (process.env.NODE_ENV === 'development') {
        // En desarrollo, cargar desde el servidor de Vite
        mainWindow.loadURL('http://localhost:5173');
        // Abrir DevTools en desarrollo
        mainWindow.webContents.openDevTools();
    } else {
        // En producción, cargar desde archivos estáticos
        mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
    }

    // Manejar cierre de ventana
    mainWindow.on('closed', () => {
        app.quit();
    });

    return mainWindow;
}

// Este método será llamado cuando Electron haya terminado la inicialización
app.whenReady().then(() => {
    createWindow();

    app.on('activate', () => {
        // En macOS, es común recrear una ventana cuando se hace clic en el icono del dock
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

// Salir cuando todas las ventanas estén cerradas, excepto en macOS
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Manejar solicitudes de MAC address desde el renderer
ipcMain.handle('get-mac-address', async () => {
    try {
        const macAddress = getRealMacAddress();
        console.log('MAC Address obtenida:', macAddress);
        return macAddress;
    } catch (error) {
        console.error('Error en get-mac-address:', error);
        return '00:00:00:00:00:00';
    }
});

// Manejar información del sistema
ipcMain.handle('get-system-info', async () => {
    try {
        return {
            platform: os.platform(),
            arch: os.arch(),
            hostname: os.hostname(),
            version: os.version(),
            macAddress: getRealMacAddress()
        };
    } catch (error) {
        console.error('Error obteniendo información del sistema:', error);
        return {
            platform: 'unknown',
            arch: 'unknown',
            hostname: 'unknown',
            version: 'unknown',
            macAddress: '00:00:00:00:00:00'
        };
    }
});

// Prevenir navegación externa
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
    });
    
    contents.on('will-navigate', (event, navigationUrl) => {
        const parsedUrl = new URL(navigationUrl);
        
        if (parsedUrl.origin !== 'http://localhost:5173' && parsedUrl.origin !== 'file://') {
            event.preventDefault();
        }
    });
});
