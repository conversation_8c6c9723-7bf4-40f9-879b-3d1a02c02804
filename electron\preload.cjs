const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Exponer APIs seguras al contexto del renderer
contextBridge.exposeInMainWorld('electronAPI', {
    // Obtener la dirección MAC real de la PC
    getMacAddress: () => ipcRenderer.invoke('get-mac-address'),
    
    // Obtener información completa del sistema
    getSystemInfo: () => ipcRenderer.invoke('get-system-info'),
    
    // Verificar si estamos en Electron
    isElectron: true,
    
    // Información de la aplicación
    appInfo: {
        name: 'Strategy Creator 2.2',
        version: '2.2.0',
        platform: process.platform
    }
});

// Prevenir que el renderer acceda directamente a Node.js
delete window.require;
delete window.exports;
delete window.module;
