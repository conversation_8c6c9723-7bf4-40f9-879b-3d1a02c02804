import React from 'react';

interface LicenseInfoProps {
  isOpen: boolean;
  onClose: () => void;
  onDeactivate: () => void;
  macAddress: string;
  activationKey: string;
  activatedAt: string;
  isElectron: boolean;
}

export const LicenseInfo: React.FC<LicenseInfoProps> = ({
  isOpen,
  onClose,
  onDeactivate,
  macAddress,
  activationKey,
  activatedAt,
  isElectron
}) => {
  if (!isOpen) return null;

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Fecha no disponible';
    }
  };

  const handleDeactivate = () => {
    if (window.confirm('¿Está seguro de que desea desactivar la licencia? Tendrá que volver a activarla para usar la aplicación.')) {
      onDeactivate();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Información de Licencia</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg className="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <span className="text-green-800 font-medium">Licencia Activada</span>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Aplicación
              </label>
              <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">
                Strategy Creator 2.2
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Dirección MAC
                {isElectron && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    ✓ Real
                  </span>
                )}
                {!isElectron && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                    ⚠ Simulada
                  </span>
                )}
              </label>
              <div className="flex items-center space-x-2">
                <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded font-mono flex-1">
                  {macAddress}
                </p>
                <button
                  onClick={() => navigator.clipboard?.writeText(macAddress)}
                  className="text-gray-400 hover:text-gray-600"
                  title="Copiar MAC"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Clave de Activación
              </label>
              <div className="flex items-center space-x-2">
                <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded font-mono flex-1">
                  {activationKey}
                </p>
                <button
                  onClick={() => navigator.clipboard?.writeText(activationKey)}
                  className="text-gray-400 hover:text-gray-600"
                  title="Copiar clave"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Activada el
              </label>
              <p className="text-sm text-gray-900 bg-gray-50 p-2 rounded">
                {formatDate(activatedAt)}
              </p>
            </div>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              onClick={onClose}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors"
            >
              Cerrar
            </button>
            <button
              onClick={handleDeactivate}
              className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
            >
              Desactivar
            </button>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            Sistema de protección HMAC SHA256
          </p>
        </div>
      </div>
    </div>
  );
};
