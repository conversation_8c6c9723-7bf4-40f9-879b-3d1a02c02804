const crypto = require('crypto');

// ⚠️ IMPORTANTE: Debe coincidir con la clave en ActivationDialog.tsx y useLicenseActivation.ts
const SECRET_KEY = 'StrategyCreator22SecretKey2025';

/**
 * Genera una clave de activación para una dirección MAC específica
 * @param {string} macAddress - Dirección MAC en formato XX:XX:XX:XX:XX:XX
 * @returns {string} Clave de activación de 12 caracteres
 */
function generateActivationKey(macAddress) {
    if (!macAddress || typeof macAddress !== 'string') {
        throw new Error('Dirección MAC requerida');
    }

    // Normalizar la MAC address (convertir a mayúsculas)
    const normalizedMac = macAddress.toUpperCase();
    
    // Validar formato de MAC address
    const macRegex = /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/;
    if (!macRegex.test(normalizedMac)) {
        throw new Error('Formato de MAC address inválido. Use XX:XX:XX:XX:XX:XX');
    }

    // Generar HMAC SHA256
    const hmac = crypto.createHmac('sha256', SECRET_KEY);
    hmac.update(normalizedMac);
    const hash = hmac.digest('hex');
    
    // Tomar los primeros 12 caracteres y convertir a mayúsculas
    const activationKey = hash.substring(0, 12).toUpperCase();
    
    return activationKey;
}

/**
 * Valida una clave de activación para una dirección MAC específica
 * @param {string} macAddress - Dirección MAC en formato XX:XX:XX:XX:XX:XX
 * @param {string} activationKey - Clave de activación a validar
 * @returns {boolean} true si la clave es válida
 */
function validateActivationKey(macAddress, activationKey) {
    try {
        const expectedKey = generateActivationKey(macAddress);
        return activationKey.toUpperCase() === expectedKey;
    } catch (error) {
        return false;
    }
}

// Función principal para uso desde línea de comandos
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('🔑 Generador de Claves de Activación - Strategy Creator 2.2');
        console.log('');
        console.log('Uso:');
        console.log('  node generate-key.cjs <MAC_ADDRESS>');
        console.log('  node generate-key.cjs <MAC_ADDRESS> <ACTIVATION_KEY> (para validar)');
        console.log('');
        console.log('Ejemplos:');
        console.log('  node generate-key.cjs 00:1B:44:11:3A:B7');
        console.log('  node generate-key.cjs 00:1B:44:11:3A:B7 A1B2C3D4E5F6');
        console.log('');
        console.log('Formato de MAC: XX:XX:XX:XX:XX:XX (con dos puntos)');
        return;
    }

    const macAddress = args[0];
    
    if (args.length === 1) {
        // Generar clave
        try {
            const activationKey = generateActivationKey(macAddress);
            console.log('✅ Clave de activación generada exitosamente');
            console.log('');
            console.log('📋 Información:');
            console.log(`   MAC Address: ${macAddress.toUpperCase()}`);
            console.log(`   Clave de Activación: ${activationKey}`);
            console.log('');
            console.log('💾 Guarde esta información de forma segura.');
            console.log('   La clave solo funcionará en el equipo con esta MAC address.');
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    } else if (args.length === 2) {
        // Validar clave
        const activationKey = args[1];
        try {
            const isValid = validateActivationKey(macAddress, activationKey);
            if (isValid) {
                console.log('✅ Clave de activación VÁLIDA');
                console.log(`   MAC Address: ${macAddress.toUpperCase()}`);
                console.log(`   Clave: ${activationKey.toUpperCase()}`);
            } else {
                console.log('❌ Clave de activación INVÁLIDA');
                console.log(`   MAC Address: ${macAddress.toUpperCase()}`);
                console.log(`   Clave: ${activationKey.toUpperCase()}`);
                process.exit(1);
            }
        } catch (error) {
            console.error('❌ Error:', error.message);
            process.exit(1);
        }
    } else {
        console.error('❌ Demasiados argumentos. Use --help para ver la ayuda.');
        process.exit(1);
    }
}

// Exportar funciones para uso como módulo
module.exports = {
    generateActivationKey,
    validateActivationKey
};

// Ejecutar función principal si se ejecuta directamente
if (require.main === module) {
    main();
}
