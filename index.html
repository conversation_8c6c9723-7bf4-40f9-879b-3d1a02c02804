<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/icon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Strategy Creator 2.2</title>
    <script src="https://cdn.tailwindcss.com"></script>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/"
  }
}
</script>
<style>
@keyframes number-color-cycle {
  0%, 100% { fill: white; }
  25% { fill: #22c55e; } /* green */
  50% { fill: #eab308; } /* yellow */
  75% { fill: #ef4444; } /* red */
}

.number-color-anim {
    animation: number-color-cycle 4s linear infinite;
}

@keyframes text-outline-cycle {
  0%, 100% {
    -webkit-text-stroke-color: var(--outline-color-1, white);
  }
  50% {
    -webkit-text-stroke-color: var(--outline-color-2, #000080);
  }
}

.text-outline-anim {
    animation: text-outline-cycle 2s linear infinite;
    -webkit-text-stroke-width: var(--outline-width, 1px);
    paint-order: stroke fill;
}

/* Custom Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 10px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.4); /* gray-400 with 40% opacity */
  border-radius: 5px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.6);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.4) transparent;
}
</style>
</head>
  <body class="bg-gray-800 text-white">
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>