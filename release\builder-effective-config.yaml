directories:
  output: release
  buildResources: assets
appId: com.strategycreator.app
productName: Strategy Creator 2.2
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - node_modules/**/*
      - '!node_modules/.cache'
      - '!node_modules/.vite'
      - '!**/*.map'
extraResources:
  - from: assets
    to: assets
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: icon.ico
  requestedExecutionLevel: asInvoker
  artifactName: ${productName}-${version}-${arch}.${ext}
  forceCodeSigning: false
  verifyUpdateCodeSignature: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  installerIcon: icon.ico
  uninstallerIcon: icon.ico
  installerHeaderIcon: icon.ico
  deleteAppDataOnUninstall: false
portable:
  artifactName: ${productName}-${version}-portable.${ext}
compression: maximum
npmRebuild: false
buildDependenciesFromSource: false
electronVersion: 37.2.3
