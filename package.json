{"name": "strategy-creator-2.2", "private": true, "version": "2.2.0", "description": "Strategy Creator 2.2 - Creador de estrategias militares con sistema de protección", "author": "Tu Nombre", "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron electron/main.cjs", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron electron/main.cjs\"", "electron-build": "npm run build && electron-builder --win --x64", "dist": "npm run build && electron-builder --publish=never --win --x64", "build-exe": "npm run build && electron-builder --win portable --x64", "clean": "rimraf dist release"}, "dependencies": {"@types/crypto-js": "^4.2.2", "crypto-js": "^4.2.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.1", "electron-builder": "^26.0.12", "rimraf": "^6.0.1", "typescript": "~5.7.2", "vite": "^6.2.0", "wait-on": "^8.0.1"}, "build": {"appId": "com.strategycreator.app", "productName": "Strategy Creator 2.2", "directories": {"output": "release", "buildResources": "assets"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*", "!node_modules/.cache", "!node_modules/.vite", "!**/*.map"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}", "forceCodeSigning": false, "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "installerIcon": "icon.ico", "uninstallerIcon": "icon.ico", "installerHeaderIcon": "icon.ico", "deleteAppDataOnUninstall": false}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "compression": "maximum", "npmRebuild": false, "buildDependenciesFromSource": false}}