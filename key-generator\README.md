# 🔑 Generador de Claves de Activación - Strategy Creator 2.2

Este directorio contiene las herramientas para generar y validar claves de activación para Strategy Creator 2.2.

## 📁 Archivos Incluidos

- `generate-key.cjs` - Script principal del generador
- `generar-clave.bat` - Interfaz gráfica en Windows
- `README.md` - Esta documentación

## 🚀 Uso Rápido

### Opción 1: Interfaz Gráfica (Windows)
```bash
# Ejecutar el archivo batch
generar-clave.bat
```

### Opción 2: Línea de Comandos
```bash
# Generar clave para una MAC específica
node generate-key.cjs 00:1B:44:11:3A:B7

# Validar una clave existente
node generate-key.cjs 00:1B:44:11:3A:B7 A1B2C3D4E5F6
```

## 📋 Cómo Obtener la MAC Address

### En Windows:
1. Abrir CMD o PowerShell
2. Ejecutar: `ipconfig /all`
3. <PERSON>car "Dirección física" de la tarjeta de red principal

### En la Aplicación:
1. Ejecutar Strategy Creator 2.2
2. En el diálogo de activación, la MAC se muestra automáticamente
3. Usar el botón "Copiar" para copiar la MAC

## 🔐 Características de Seguridad

- **HMAC SHA256**: Algoritmo criptográfico robusto
- **Vinculación por Hardware**: Cada clave solo funciona en un equipo específico
- **Clave Secreta**: Protegida en el código de la aplicación
- **Validación Local**: No requiere conexión a internet

## 📝 Formato de Datos

### MAC Address
- Formato: `XX:XX:XX:XX:XX:XX`
- Ejemplo: `00:1B:44:11:3A:B7`
- Debe incluir los dos puntos (`:`)

### Clave de Activación
- Longitud: 12 caracteres hexadecimales
- Formato: Solo letras A-F y números 0-9
- Ejemplo: `A1B2C3D4E5F6`

## 🛠️ Ejemplos de Uso

### Generar Clave
```bash
$ node generate-key.cjs 00:1B:44:11:3A:B7

✅ Clave de activación generada exitosamente

📋 Información:
   MAC Address: 00:1B:44:11:3A:B7
   Clave de Activación: A1B2C3D4E5F6

💾 Guarde esta información de forma segura.
   La clave solo funcionará en el equipo con esta MAC address.
```

### Validar Clave
```bash
$ node generate-key.cjs 00:1B:44:11:3A:B7 A1B2C3D4E5F6

✅ Clave de activación VÁLIDA
   MAC Address: 00:1B:44:11:3A:B7
   Clave: A1B2C3D4E5F6
```

## ⚠️ Notas Importantes

1. **Seguridad**: Mantenga este generador en un lugar seguro
2. **Respaldo**: Guarde las claves generadas en un archivo seguro
3. **Transferencia**: Las claves NO funcionan si se transfiere a otro equipo
4. **Validación**: Siempre valide las claves antes de entregarlas

## 🔧 Requisitos

- Node.js instalado en el sistema
- Acceso a la línea de comandos
- Windows (para el archivo .bat)

## 📞 Soporte

Si tiene problemas con el generador:

1. Verifique que Node.js esté instalado
2. Asegúrese de que el formato de MAC sea correcto
3. Verifique que la clave secreta coincida en todos los archivos

---

**Strategy Creator 2.2** - Sistema de Protección Avanzado
