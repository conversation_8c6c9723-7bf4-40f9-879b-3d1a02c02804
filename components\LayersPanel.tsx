import React, { useState, useMemo, useRef, useEffect } from 'react';
import { type TokenState, type Annotation, UnitType, AnnotationType, OtherType, type LayerEntry, type LayerItemIdentifier, type LayerGroup } from '../types';
import { 
    SwordIcon, TextIcon as UnitTextIcon, 
    MortarIcon
} from './icons/UnitIcons';
import { ArrowIcon, CircleIcon, EyeIcon, EyeSlashIcon, TrashIcon, ChevronRightIcon, LayersIcon, ChevronDownIcon, PlusIcon, SkipToStartIcon, PlayIcon, PauseIcon } from './icons/ToolIcons';

interface LayersPanelProps {
  layers: LayerEntry[];
  tokens: TokenState[];
  annotations: Annotation[];
  selectedTokenId: number | null;
  onSelectToken: (id: number) => void;
  onToggleVisibility: (id: number, type: 'token' | 'annotation') => void;
  onToggleGroupVisibility: (groupId: number) => void;
  onDeleteItem: (id: number, type: 'token' | 'annotation') => void;
  onLayersUpdate: (layers: LayerEntry[]) => void;
  getNextId: () => number;
  onResetTokenAnimation: (tokenId: number) => void;
  onToggleTokenAnimation: (tokenId: number) => void;
  onResetGroupAnimation: (groupId: number) => void;
  onToggleGroupAnimation: (groupId: number) => void;
  t: (key: string) => string;
}

const itemIcons: Record<string, React.ReactNode> = {
  [UnitType.Sword]: <SwordIcon />,
  [UnitType.Text]: <UnitTextIcon />,
  [UnitType.Other]: <MortarIcon />,
  [AnnotationType.Arrow]: <ArrowIcon />,
  [AnnotationType.Circle]: <CircleIcon />,
  'group': <LayersIcon />,
  [UnitType.Custom]: <SwordIcon/>,
  [UnitType.CustomOther]: <MortarIcon/>
};

const otherIcons: Record<OtherType, React.ReactNode> = {
    [OtherType.Mortar]: <MortarIcon />,
};

const hexToRgba = (hex: string, alpha: number): string => {
    if(!hex) return `rgba(100, 116, 139, ${alpha})`;
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

const getAllItemIdsFromGroup = (group: LayerGroup): { tokens: Set<number>, annotations: Set<number> } => {
    const ids: { tokens: Set<number>, annotations: Set<number> } = { tokens: new Set(), annotations: new Set() };
    const collect = (items: LayerEntry[]) => {
        for (const item of items) {
            if (item.type === 'group') {
                collect(item.items);
            } else if (item.type === 'token') {
                ids.tokens.add(item.id);
            } else {
                ids.annotations.add(item.id);
            }
        }
    };
    collect(group.items);
    return ids;
};

const getItemData = (
    itemIdentifier: LayerEntry,
    itemMap: Map<string, TokenState | Annotation>,
    t: (key: string) => string
): { name: string; icon: React.ReactNode; color: string, isVisible: boolean, id: number } => {
    if (itemIdentifier.type === 'group') {
        const allIds = getAllItemIdsFromGroup(itemIdentifier);
        const isGroupVisible = [...allIds.tokens].some(id => itemMap.get(`token-${id}`)?.isVisible ?? true) ||
                               [...allIds.annotations].some(id => itemMap.get(`annotation-${id}`)?.isVisible ?? true);
        return { name: itemIdentifier.name, icon: itemIcons.group, color: '#94a3b8', isVisible: isGroupVisible, id: itemIdentifier.id };
    }

    const item = itemMap.get(`${itemIdentifier.type}-${itemIdentifier.id}`);
    if (!item) {
        return { name: 'Unknown', icon: <></>, color: '#94a3b8', isVisible: false, id: -1 };
    }

    let icon = itemIcons[item.type];
    if (item.type === UnitType.Other && 'otherType' in item && item.otherType) {
        icon = otherIcons[item.otherType] ?? itemIcons[UnitType.Other];
    } else if (item.type === UnitType.Custom || item.type === UnitType.CustomOther){
        icon = item.type === UnitType.Custom ? itemIcons[UnitType.Custom] : itemIcons[UnitType.CustomOther]
    }

    let name: string;
    if ('name' in item && item.name) {
        name = item.name;
    } else if (item.type === UnitType.Text && 'text' in item) {
        name = item.text || t('unit_text');
    } else if (item.type === UnitType.Other && 'otherType' in item && item.otherType) {
        const key = `other_${item.otherType.toLowerCase().replace(/ /g, '_')}`;
        name = `${t(key)} ${item.id}`;
    } else if (item.type === AnnotationType.Arrow || item.type === AnnotationType.Circle) {
        const key = `annotation_${item.type.toLowerCase()}`;
        name = `${t(key)} ${item.id}`;
    } else {
        const key = `unit_${(item.type as UnitType).toLowerCase()}`;
        name = `${t(key)} ${item.id}`;
    }
    
    return { name, icon, color: item.color, isVisible: item.isVisible ?? true, id: item.id };
};


export const LayersPanel: React.FC<LayersPanelProps> = ({
  layers, tokens, annotations, selectedTokenId, onSelectToken,
  onToggleVisibility, onToggleGroupVisibility, onDeleteItem, onLayersUpdate, getNextId, onResetTokenAnimation,
  onToggleTokenAnimation, onResetGroupAnimation, onToggleGroupAnimation, t
}) => {
    const [isCollapsed, setIsCollapsed] = useState(true);
    const [isCreatingGroup, setIsCreatingGroup] = useState(false);
    const [newGroupName, setNewGroupName] = useState('');
    const [activeDropdownPath, setActiveDropdownPath] = useState<string | null>(null);
    const [dropdownPositionClass, setDropdownPositionClass] = useState('bottom-full mb-1');
    const dropdownRef = useRef<HTMLDivElement>(null);

    const itemMap = useMemo(() => {
        const map = new Map<string, TokenState | Annotation>();
        tokens.forEach(t => map.set(`token-${t.id}`, t));
        annotations.forEach(a => map.set(`annotation-${a.id}`, a));
        return map;
    }, [tokens, annotations]);

    const groups = useMemo(() => {
        const collectedGroups: LayerGroup[] = [];
        const findGroups = (entries: LayerEntry[]) => {
            entries.forEach(entry => {
                if (entry.type === 'group') {
                    collectedGroups.push(entry);
                    findGroups(entry.items);
                }
            });
        };
        findGroups(layers);
        return collectedGroups;
    }, [layers]);
    
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (activeDropdownPath && dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setActiveDropdownPath(null);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [activeDropdownPath]);

    const handleCreateGroup = () => {
        const trimmedName = newGroupName.trim();
        if (!trimmedName) return;
    
        const existingGroupNames = new Set(groups.map(g => g.name));
    
        let finalName = trimmedName;
        if (existingGroupNames.has(finalName)) {
            let counter = 2;
            while (existingGroupNames.has(`${trimmedName} (${counter})`)) {
                counter++;
            }
            finalName = `${trimmedName} (${counter})`;
        }
    
        const newGroup: LayerGroup = {
            id: getNextId(),
            type: 'group',
            name: finalName,
            items: [],
            isCollapsed: false,
        };
    
        onLayersUpdate([newGroup, ...layers]);
        setIsCreatingGroup(false);
        setNewGroupName('');
    };

    const handleAssignToGroup = (itemIdent: LayerItemIdentifier, targetGroupId: number | null) => {
        let newLayers = JSON.parse(JSON.stringify(layers));
        let itemToMove: LayerItemIdentifier | null = null;
    
        const findAndRemove = (entries: LayerEntry[]): LayerEntry[] => {
            const remaining: LayerEntry[] = [];
            for (const entry of entries) {
                if (entry.type !== 'group' && entry.id === itemIdent.id && entry.type === itemIdent.type) {
                    itemToMove = entry;
                } else {
                    if (entry.type === 'group') {
                        entry.items = findAndRemove(entry.items);
                    }
                    remaining.push(entry);
                }
            }
            return remaining;
        };
        newLayers = findAndRemove(newLayers);
        
        if (!itemToMove) return;
    
        if (targetGroupId === null) { // Move to root
            newLayers.unshift(itemToMove);
        } else {
            let targetGroupFound = false;
            const addToGroup = (entries: LayerEntry[]) => {
                for (const entry of entries) {
                    if (entry.type === 'group' && entry.id === targetGroupId) {
                        entry.items.unshift(itemToMove!);
                        targetGroupFound = true;
                        return true;
                    }
                    if (entry.type === 'group') {
                       if(addToGroup(entry.items)) return true;
                    }
                }
                return false;
            };
            
            if (!addToGroup(newLayers)) {
                newLayers.unshift(itemToMove); // Fallback to root if group not found
            }
        }
        
        const cleanupEmptyGroups = (entries: LayerEntry[]): LayerEntry[] => {
            return entries.map(e => {
                if (e.type === 'group') {
                    e.items = cleanupEmptyGroups(e.items);
                }
                return e;
            }).filter(e => e.type !== 'group' || e.items.length > 0);
        };
    
        onLayersUpdate(cleanupEmptyGroups(newLayers));
        setActiveDropdownPath(null);
    };

    const handleToggleDropdown = (e: React.MouseEvent, path: string) => {
        e.stopPropagation();
        if (activeDropdownPath === path) {
            setActiveDropdownPath(null);
            return;
        }
    
        const buttonElement = e.currentTarget;
        const rect = buttonElement.getBoundingClientRect();
        
        const dropdownHeight = Math.min(192, (groups.length + 1) * 32 + 16); // 192px is max-h-48, 32px per item
    
        if (window.innerHeight - rect.bottom < dropdownHeight) {
             setDropdownPositionClass('bottom-full mb-1');
        } else {
            setDropdownPositionClass('top-full mt-1');
        }
        
        setActiveDropdownPath(path);
    };
    
    const toggleCollapse = (path: string) => {
        const newLayers = JSON.parse(JSON.stringify(layers));
        const indices = path.split('-').map(Number);
        let current: any = { items: newLayers };
        try {
            indices.forEach(index => {
                current = current.items[index];
            });
            if (current && current.type === 'group') {
                current.isCollapsed = !current.isCollapsed;
            }
            onLayersUpdate(newLayers);
        } catch (e) {
            console.error("Error toggling collapse", e)
        }
    };

    const flatItems: { item: LayerEntry; path: string; depth: number }[] = [];
    const buildFlatList = (entries: LayerEntry[], pathPrefix: string, depth: number) => {
        const reversedEntries = [...entries].reverse();
        reversedEntries.forEach((entry, reversedIndex) => {
            const originalIndex = entries.length - 1 - reversedIndex;
            const currentPath = pathPrefix ? `${pathPrefix}-${originalIndex}` : `${originalIndex}`;
            
            flatItems.push({ item: entry, path: currentPath, depth });
            
            if (entry.type === 'group' && !entry.isCollapsed) {
                buildFlatList(entry.items, currentPath, depth + 1);
            }
        });
    };
    buildFlatList(layers, '', 0);

    if (isCollapsed) {
        return (
            <div className="absolute top-4 right-4 z-30">
                <button
                    onClick={() => setIsCollapsed(false)}
                    title={t('layers_open')}
                    className="w-14 h-14 bg-blue-600 rounded-xl shadow-lg flex items-center justify-center transform rotate-45 hover:scale-110 transition-all duration-200 ease-in-out"
                >
                    <div className="transform -rotate-45 text-white w-8 h-8"><LayersIcon /></div>
                </button>
            </div>
        );
    }
    
  return (
    <aside className="absolute top-4 right-4 z-30 w-72 bg-gray-900/70 backdrop-blur-md rounded-lg shadow-2xl flex flex-col max-h-[calc(100vh-32px)]">
        <div className="flex items-center justify-between p-3 border-b border-white/10 flex-shrink-0">
            <h1 className="text-lg font-bold text-blue-400">{t('layers_title')}</h1>
            <div className="flex items-center space-x-2">
                {isCreatingGroup ? (
                    <div className="flex items-center">
                        <input
                            type="text"
                            value={newGroupName}
                            onChange={(e) => setNewGroupName(e.target.value)}
                            onKeyDown={(e) => { if (e.key === 'Enter') handleCreateGroup(); if (e.key === 'Escape') setIsCreatingGroup(false); }}
                            className="bg-gray-700 text-white text-sm p-1 rounded-l-md outline-none w-28"
                            autoFocus
                        />
                        <button onClick={handleCreateGroup} className="bg-blue-600 hover:bg-blue-500 p-1.5 rounded-r-md text-white">✓</button>
                        <button onClick={() => setIsCreatingGroup(false)} className="bg-red-600 hover:bg-red-500 p-1.5 rounded-md ml-1 text-white">X</button>
                    </div>
                ) : (
                    <button onClick={() => { setNewGroupName(t('layers_new_group_default_name')); setIsCreatingGroup(true); }} title={t('layers_create_group')} className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-white/10 transition-colors"><PlusIcon /></button>
                )}
                 <button onClick={() => setIsCollapsed(true)} title={t('layers_collapse')} className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-white/10 transition-colors"><ChevronRightIcon /></button>
            </div>
        </div>
      
        <div className="overflow-y-auto p-2 custom-scrollbar">
            {flatItems.length === 0 && <p className="text-xs text-gray-500 px-2 py-1">{t('layers_no_items')}</p>}
            {flatItems.map(({ item, path, depth }) => {
                const data = getItemData(item, itemMap, t);
                const isGroup = item.type === 'group';
                const isSelected = !isGroup && selectedTokenId === (item as LayerItemIdentifier).id;
                
                const token = !isGroup && item.type === 'token' ? itemMap.get(`token-${(item as LayerItemIdentifier).id}`) as TokenState : null;
                const hasPath = !!(token && token.path && token.path.length > 1);
                const isAnimating = token?.isAnimating ?? true;

                let hasAnimatableTokensInGroup = false;
                let isAnyAnimatingInGroup = false;

                if (isGroup) {
                    const allIds = getAllItemIdsFromGroup(item as LayerGroup);
                    const animatableTokens = [...allIds.tokens]
                        .map(id => itemMap.get(`token-${id}`))
                        .filter((t): t is TokenState => !!(t && 'path' in t && t.path.length > 1));
                    
                    hasAnimatableTokensInGroup = animatableTokens.length > 0;
                    isAnyAnimatingInGroup = hasAnimatableTokensInGroup && animatableTokens.some(t => t.isAnimating ?? true);
                }

                return (
                    <div
                        key={path}
                        onClick={() => {
                          if (isGroup) toggleCollapse(path);
                          else onSelectToken(data.id);
                        }}
                        className={`flex items-center p-1.5 my-0.5 rounded-md cursor-pointer transition-all duration-150 ${isSelected ? 'ring-2 ring-blue-400' : ''}`}
                        style={{ marginLeft: `${depth * 16}px`, backgroundColor: hexToRgba(data.color, 0.2) }}
                    >
                        {isGroup && (
                          <button onClick={(e) => { e.stopPropagation(); toggleCollapse(path); }} className={`w-4 h-4 mr-1 text-gray-300 transition-transform ${(item as LayerGroup).isCollapsed ? '' : 'rotate-90'}`}>
                            <ChevronRightIcon />
                          </button>
                        )}
                        <div className="w-4 h-4 mr-2 flex-shrink-0" style={{ color: isGroup ? 'white' : data.color }}>{data.icon}</div>
                        <span className="flex-grow truncate text-xs font-medium text-gray-200">{data.name}</span>
                        
                        <div className="flex items-center flex-shrink-0 ml-2">
                             {isGroup && (
                                <div className="flex items-center">
                                    {hasAnimatableTokensInGroup && (
                                        <>
                                            <button onClick={(e) => { e.stopPropagation(); onToggleGroupAnimation(item.id); }} title={isAnyAnimatingInGroup ? t('layers_pause_group_animation') : t('layers_play_group_animation')} className="p-1 text-gray-400 hover:text-white">
                                                <div className="w-4 h-4">{isAnyAnimatingInGroup ? <PauseIcon /> : <PlayIcon />}</div>
                                            </button>
                                            <button onClick={(e) => { e.stopPropagation(); onResetGroupAnimation(item.id); }} title={t('layers_reset_group_position')} className="p-1 text-gray-400 hover:text-white">
                                                <div className="w-4 h-4"><SkipToStartIcon /></div>
                                            </button>
                                        </>
                                    )}
                                    <button onClick={(e) => { e.stopPropagation(); onToggleGroupVisibility(item.id); }} title={t('layers_toggle_visibility')} className="p-1 text-gray-400 hover:text-white"><div className="w-4 h-4">{data.isVisible ? <EyeIcon /> : <EyeSlashIcon />}</div></button>
                                </div>
                            )}
                            {!isGroup && (
                                <div className="flex items-center">
                                    {hasPath && (
                                        <>
                                            <button onClick={(e) => { e.stopPropagation(); onToggleTokenAnimation(data.id); }} title={isAnimating ? t('layers_pause_animation') : t('layers_play_animation')} className="p-1 text-gray-400 hover:text-white">
                                                <div className="w-4 h-4">{isAnimating ? <PauseIcon /> : <PlayIcon />}</div>
                                            </button>
                                            <button onClick={(e) => { e.stopPropagation(); onResetTokenAnimation(data.id); }} title={t('layers_reset_position')} className="p-1 text-gray-400 hover:text-white">
                                                <div className="w-4 h-4"><SkipToStartIcon /></div>
                                            </button>
                                        </>
                                    )}
                                    <div className="relative">
                                        <button onClick={(e) => handleToggleDropdown(e, path)} title={t('layers_assign_to_group')} className="p-1 text-gray-400 hover:text-white"><div className="w-4 h-4"><LayersIcon /></div></button>
                                        {activeDropdownPath === path && (
                                            <div ref={dropdownRef} className={`absolute right-0 w-40 bg-gray-800 border border-gray-600 rounded-md shadow-lg z-20 max-h-48 overflow-y-auto ${dropdownPositionClass} custom-scrollbar`}>
                                                <div onClick={(e) => { e.stopPropagation(); handleAssignToGroup(item as LayerItemIdentifier, null); }} className="px-3 py-1.5 text-sm text-white hover:bg-gray-700 cursor-pointer">{t('layers_group_none')}</div>
                                                {groups.filter(g => g.id !== item.id).map(g => ( // Prevent assigning to self if it were possible
                                                    <div key={g.id} onClick={(e) => { e.stopPropagation(); handleAssignToGroup(item as LayerItemIdentifier, g.id); }} className="px-3 py-1.5 text-sm text-white hover:bg-gray-700 cursor-pointer">{g.name}</div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    <button onClick={(e) => { e.stopPropagation(); onToggleVisibility(data.id, (item as LayerItemIdentifier).type); }} title={t('layers_toggle_visibility')} className="p-1 text-gray-400 hover:text-white"><div className="w-4 h-4">{data.isVisible ? <EyeIcon /> : <EyeSlashIcon />}</div></button>
                                    <button onClick={(e) => { e.stopPropagation(); onDeleteItem(data.id, (item as LayerItemIdentifier).type); }} title={t('layers_delete_item')} className="p-1 text-gray-400 hover:text-red-500"><div className="w-4 h-4"><TrashIcon /></div></button>
                                </div>
                            )}
                        </div>
                    </div>
                );
            })}
      </div>
    </aside>
  );
};