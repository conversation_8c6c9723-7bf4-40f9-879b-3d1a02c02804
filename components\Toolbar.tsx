
import React, { useState, useRef, useEffect } from 'react';
import { type TokenState, type Tool, type TextPreset, UnitType as EUnitType, OtherType, type CustomUnit, type ArrowSettings, Point } from '../types';
import { UNIT_TYPES, FONT_FACES, OTHER_TYPES } from '../constants';
import { 
    SwordIcon, TextIcon, 
    MortarIcon
} from './icons/UnitIcons';
import { 
    SelectIcon, MoveIcon, CloneIcon, ZoomIcon, ArrowIcon, CircleIcon, PathIcon, TrashIcon, UploadIcon, EraserIcon, UndoIcon, RedoIcon, 
    BookmarkSquareIcon, ChevronDownIcon, ChevronLeftIcon, ChevronRightIcon, PlusIcon, MinusIcon, ExportIcon, ImportIcon, EnlargeIcon
} from './icons/ToolIcons';
import { UsaFlagIcon, PeruFlagIcon } from './icons/FlagIcons';

interface NewTextState {
    content: string;
    size: number;
    fontFamily: string;
    outlineColor1: string;
    outlineColor2: string;
    outlineWidth: number;
}

interface ToolbarProps {
  activeTool: Tool;
  onToolSelect: (tool: Tool) => void;
  onMapUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClearAll: () => void;
  selectedColor1: string;
  onColor1Change: (color: string) => void;
  selectedColor2: string;
  onColor2Change: (color: string) => void;
  paletteColors: string[];
  onPaletteColorChange: (index: number, newColor: string) => void;
  onUndo: () => void;
  onRedo: () => void;
  newText: NewTextState;
  onNewTextChange: (state: NewTextState) => void;
  textPresets: TextPreset[];
  onAddTextPreset: () => void;
  onDeleteTextPreset: (id: number) => void;
  onLoadTextPreset: (preset: TextPreset) => void;
  arrowSettings: ArrowSettings;
  onArrowSettingsChange: (settings: ArrowSettings) => void;
  selectedToken: TokenState | null;
  onTokenUpdate: (tokenId: number, updates: Partial<TokenState>) => void;
  onClearPath: (tokenId: number) => void;
  onExportProject: () => void;
  onImportProject: (event: React.ChangeEvent<HTMLInputElement>) => void;
  customUnits: CustomUnit[];
  onCustomUnitUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onDeleteCustomUnit: (id: number) => void;
  customOthers: CustomUnit[];
  onCustomOtherUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onDeleteCustomOther: (id: number) => void;
  globalTokenSize: number;
  onGlobalTokenSizeChange: (size: number) => void;
  language: 'en' | 'es';
  onLanguageChange: () => void;
  arePathsVisible: boolean;
  onArePathsVisibleChange: (visible: boolean) => void;
  t: (key: string) => string;
}

const unitIcons: Record<string, React.ReactNode> = {
  [EUnitType.Sword]: <SwordIcon />,
};

const otherIcons: Record<OtherType, React.ReactNode> = {
  [OtherType.Mortar]: <MortarIcon />,
};

const ToolButton: React.FC<{
  label: string;
  isActive: boolean;
  onClick: () => void;
  children: React.ReactNode;
}> = ({ label, isActive, onClick, children }) => (
  <button
    onClick={onClick}
    title={label}
    className={`flex items-center justify-start w-full p-3 my-1 rounded-lg transition-colors duration-200 ${
      isActive ? 'bg-blue-600 text-white' : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
    }`}
  >
    <div className="w-6 h-6 mr-3">{children}</div>
    <span className="font-medium">{label}</span>
  </button>
);

const getArrowheadPoints = (endPoint: {x:number, y:number}, controlPoint: {x:number, y:number}, length: number, width: number): string => {
    const dirX = endPoint.x - controlPoint.x;
    const dirY = endPoint.y - controlPoint.y;
    const len = Math.hypot(dirX, dirY);
    if (len === 0) return "";
    
    const normX = dirX / len;
    const normY = dirY / len;
    
    const arrowLength = length;
    const arrowWidth = width;

    const tip = endPoint;
    const baseCenterX = tip.x - normX * arrowLength;
    const baseCenterY = tip.y - normY * arrowLength;
    const perpX = -normY;
    const perpY = normX;
    const p1x = baseCenterX + perpX * (arrowWidth / 2);
    const p1y = baseCenterY + perpY * (arrowWidth / 2);
    const p2x = baseCenterX - perpX * (arrowWidth / 2);
    const p2y = baseCenterY - perpY * (arrowWidth / 2);
    return `${tip.x},${tip.y} ${p1x},${p1y} ${p2x},${p2y}`;
};

const findIntersectionT = (P0: Point, P1: Point, P2: Point, arrowheadLength: number): number => {
    const dirX = P2.x - P1.x;
    const dirY = P2.y - P1.y;
    const len = Math.hypot(dirX, dirY);
    if (len < 1e-6) return 1.0; 

    const normDirX = dirX / len;
    const normDirY = dirY / len;

    const baseCenterX = P2.x - normDirX * arrowheadLength;
    const baseCenterY = P2.y - normDirY * arrowheadLength;

    const Ax = P0.x - 2 * P1.x + P2.x;
    const Bx = 2 * (P1.x - P0.x);
    const Cx = P0.x;
    const Ay = P0.y - 2 * P1.y + P2.y;
    const By = 2 * (P1.y - P0.y);
    const Cy = P0.y;

    const A = normDirX * Ax + normDirY * Ay;
    const B = normDirX * Bx + normDirY * By;
    const C = normDirX * (Cx - baseCenterX) + normDirY * (Cy - baseCenterY);
    
    if (Math.abs(A) < 1e-6) {
        if (Math.abs(B) < 1e-6) return 1.0;
        const t = -C / B;
        return (t >= 0 && t <= 1) ? t : 1.0;
    }

    const discriminant = B * B - 4 * A * C;
    if (discriminant < 0) return 1.0;

    const sqrtDiscriminant = Math.sqrt(discriminant);
    const t1 = (-B + sqrtDiscriminant) / (2 * A);
    const t2 = (-B - sqrtDiscriminant) / (2 * A);

    const t1Valid = t1 >= 0 && t1 <= 1;
    const t2Valid = t2 >= 0 && t2 <= 1;

    if (t1Valid && t2Valid) return Math.max(t1, t2);
    if (t1Valid) return t1;
    if (t2Valid) return t2;
    
    return 1.0;
};

const getTaperedArrowBodyPath = (start: Point, control: Point, end: Point, widthStart: number, widthEnd: number, t_end: number): string => {
    if (t_end <= 0) return "";
    const segments = 20;
    const points1: Point[] = [];
    const points2: Point[] = [];

    const curve = (t: number, p0: number, p1: number, p2: number) => (1 - t) ** 2 * p0 + 2 * (1 - t) * t * p1 + t ** 2 * p2;
    const derivative = (t: number, p0: number, p1: number, p2: number) => 2 * (1 - t) * (p1 - p0) + 2 * t * (p2 - p1);

    for (let i = 0; i <= segments; i++) {
        const t = (i / segments) * t_end;
        if (t > t_end) continue;

        const px = curve(t, start.x, control.x, end.x);
        const py = curve(t, start.y, control.y, end.y);
        
        let dx = derivative(t, start.x, control.x, end.x);
        let dy = derivative(t, start.y, control.y, end.y);

        const len = Math.hypot(dx, dy);
        if (len < 1e-6) {
             if (i > 0) {
                const prevT = ((i-1)/segments) * t_end;
                dx = derivative(prevT, start.x, control.x, end.x);
                dy = derivative(prevT, start.y, control.y, end.y);
            } else {
                dx = control.x - start.x;
                dy = control.y - start.y;
            }
            if(Math.hypot(dx, dy) < 1e-6) continue;
        }

        const normDx = dx / Math.hypot(dx, dy);
        const normDy = dy / Math.hypot(dx, dy);
        
        const nx = -normDy;
        const ny = normDx;
        
        const currentWidth = widthStart + (widthEnd - widthStart) * (t / t_end);
        const halfWidth = Math.max(0, currentWidth) / 2;
        
        points1.push({ x: px + nx * halfWidth, y: py + ny * halfWidth });
        points2.push({ x: px - nx * halfWidth, y: py - ny * halfWidth });
    }

    if (points1.length < 2 || points2.length < 2) return "";

    const pathParts = [`M ${points1[0].x} ${points1[0].y}`];
    for (let i = 1; i < points1.length; i++) pathParts.push(`L ${points1[i].x} ${points1[i].y}`);
    
    points2.reverse();
    for (let i = 0; i < points2.length; i++) pathParts.push(`L ${points2[i].x} ${points2[i].y}`);
    
    pathParts.push('Z');
    return pathParts.join(' ');
};

export const Toolbar: React.FC<ToolbarProps> = ({
  activeTool,
  onToolSelect,
  onMapUpload,
  onClearAll,
  selectedColor1,
  onColor1Change,
  selectedColor2,
  onColor2Change,
  paletteColors,
  onPaletteColorChange,
  onUndo,
  onRedo,
  newText,
  onNewTextChange,
  textPresets,
  onAddTextPreset,
  onDeleteTextPreset,
  onLoadTextPreset,
  arrowSettings,
  onArrowSettingsChange,
  selectedToken,
  onTokenUpdate,
  onClearPath,
  onExportProject,
  onImportProject,
  customUnits,
  onCustomUnitUpload,
  onDeleteCustomUnit,
  customOthers,
  onCustomOtherUpload,
  onDeleteCustomOther,
  globalTokenSize,
  onGlobalTokenSizeChange,
  language,
  onLanguageChange,
  arePathsVisible,
  onArePathsVisibleChange,
  t,
}) => {
  const [isPresetListOpen, setIsPresetListOpen] = useState(false);
  const colorInputRef = useRef<HTMLInputElement>(null);
  const editingColorIndexRef = useRef<number | null>(null);
  const [pendingColorChange, setPendingColorChange] = useState<{ index: number; color: string } | null>(null);
  const [activeSwatch, setActiveSwatch] = useState<1 | 2>(1);
  const [previewUnit, setPreviewUnit] = useState<TokenState | null>(null);
  const [isUnitTrashHovered, setIsUnitTrashHovered] = useState(false);
  const [isOtherTrashHovered, setIsOtherTrashHovered] = useState(false);
  const [isToolbarCollapsed, setIsToolbarCollapsed] = useState(false);
  const [isToolsCollapsed, setIsToolsCollapsed] = useState(false);

  useEffect(() => {
    if (selectedToken) {
        setPreviewUnit(null);
    }
  }, [selectedToken]);

  useEffect(() => {
    if (previewUnit) {
        setPreviewUnit(prev => prev ? { 
            ...prev,
            color: selectedColor1,
            outlineColor1: selectedColor1,
            outlineColor2: selectedColor2
        } : null);
    }
  }, [selectedColor1, selectedColor2, previewUnit]);

  const handleUnitPreview = (unitType: EUnitType, options: Partial<TokenState> = {}) => {
    setPreviewUnit({
        id: 0, // temporary ID
        type: unitType,
        color: selectedColor1,
        number: null,
        position: { x: 0, y: 0 },
        path: [],
        animationProgress: 0,
        patrolForward: true,
        size: 1,
        rotation: 0,
        text: 'Preview',
        fontFamily: 'Arial',
        outlineColor1: selectedColor1,
        outlineColor2: selectedColor2,
        outlineWidth: 0.15,
        isVisible: true,
        animationSpeed: 0.20,
        isPatrol: false,
        isFlipped: false,
        isAnimating: true,
        name: 'Preview',
        ...options,
    });
  };

  const handleDisplayTokenUpdate = (updates: Partial<TokenState>) => {
    if (selectedToken) {
        onTokenUpdate(selectedToken.id, updates);
    } else if (previewUnit) {
        setPreviewUnit(prev => prev ? { ...prev, ...updates } : null);
    }
  };
  
  const displayToken = selectedToken || previewUnit;

  const handleUnitDragStart = (e: React.DragEvent, type: EUnitType) => {
    let dataToDrag: any = { type };
    if (previewUnit && previewUnit.type === type) {
        dataToDrag = {
            ...dataToDrag,
            rotation: previewUnit.rotation,
            animationSpeed: previewUnit.animationSpeed,
            isPatrol: previewUnit.isPatrol,
            isFlipped: previewUnit.isFlipped,
            outlineWidth: previewUnit.outlineWidth,
        };
    }
    const data = JSON.stringify(dataToDrag);
    e.dataTransfer.setData('application/json', data);
  };

  const handleCustomUnitDragStart = (e: React.DragEvent, unit: CustomUnit) => {
    let dataToDrag: any = { 
        type: EUnitType.Custom, 
        customImage: unit.imageData, 
        name: unit.name,
        isDeletable: true,
        id: unit.id,
        listType: 'unit'
    };
    if (previewUnit && previewUnit.type === EUnitType.Custom && previewUnit.name === unit.name) {
        dataToDrag = {
            ...dataToDrag,
            rotation: previewUnit.rotation,
            animationSpeed: previewUnit.animationSpeed,
            isPatrol: previewUnit.isPatrol,
            isFlipped: previewUnit.isFlipped,
            outlineWidth: previewUnit.outlineWidth,
        };
    }
    const data = JSON.stringify(dataToDrag);
    e.dataTransfer.setData('application/json', data);
  };

  const handleOtherDragStart = (e: React.DragEvent, otherType: OtherType) => {
    let dataToDrag: any = { type: EUnitType.Other, otherType };
    if (previewUnit && previewUnit.type === EUnitType.Other && previewUnit.otherType === otherType) {
        dataToDrag = {
            ...dataToDrag,
            rotation: previewUnit.rotation,
            animationSpeed: previewUnit.animationSpeed,
            isPatrol: previewUnit.isPatrol,
            isFlipped: previewUnit.isFlipped,
            outlineWidth: previewUnit.outlineWidth,
        };
    }
    const data = JSON.stringify(dataToDrag);
    e.dataTransfer.setData('application/json', data);
  };

  const handleCustomOtherDragStart = (e: React.DragEvent, unit: CustomUnit) => {
    let dataToDrag: any = { 
        type: EUnitType.CustomOther, 
        customImage: unit.imageData, 
        name: unit.name,
        isDeletable: true,
        id: unit.id,
        listType: 'other'
    };
    if (previewUnit && previewUnit.type === EUnitType.CustomOther && previewUnit.name === unit.name) {
        dataToDrag = {
            ...dataToDrag,
            rotation: previewUnit.rotation,
            animationSpeed: previewUnit.animationSpeed,
            isPatrol: previewUnit.isPatrol,
            isFlipped: previewUnit.isFlipped,
            outlineWidth: previewUnit.outlineWidth,
        };
    }
    const data = JSON.stringify(dataToDrag);
    e.dataTransfer.setData('application/json', data);
  };

  const handleTextDragStart = (e: React.DragEvent) => {
    const data = JSON.stringify({
      isTextToken: true,
      content: newText.content,
      size: newText.size,
      fontFamily: newText.fontFamily,
      outlineColor1: newText.outlineColor1,
      outlineColor2: newText.outlineColor2,
      outlineWidth: newText.outlineWidth,
    });
    e.dataTransfer.setData('application/json', data);
  };

  const handleTextSizeChange = (amount: number) => {
    const newSize = Math.max(0.5, Math.min(5, newText.size + amount));
    onNewTextChange({ ...newText, size: parseFloat(newSize.toFixed(2)) });
  };
  
  const handleColorDoubleClick = (index: number) => {
    if (pendingColorChange) return;
    editingColorIndexRef.current = index;
    colorInputRef.current?.click();
  };

  const handleColorInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (editingColorIndexRef.current !== null) {
      setPendingColorChange({ index: editingColorIndexRef.current, color: e.target.value });
    }
  };

  const handleConfirmColorChange = () => {
    if (pendingColorChange) {
      onPaletteColorChange(pendingColorChange.index, pendingColorChange.color);
      setPendingColorChange(null);
      editingColorIndexRef.current = null;
    }
  };

  const handleCancelColorChange = () => {
    setPendingColorChange(null);
    editingColorIndexRef.current = null;
  };

  const ArrowPreview = () => {
    const start = { x: 10, y: 30 };
    const control = { x: 50, y: 10 };
    const end = { x: 90, y: 30 };
    
    const { 
        strokeWidthStart,
        strokeWidthEnd,
        arrowheadLength, 
        arrowheadWidth, 
        isAnimated, 
        animatingCircleRadius, 
        animatingCircleColor, 
        animationDuration
    } = arrowSettings;
    
    const intersectionT = findIntersectionT(start, control, end, arrowheadLength);
    const bodyPathData = getTaperedArrowBodyPath(start, control, end, strokeWidthStart, strokeWidthEnd, intersectionT);
    const animationPathData = `M ${start.x} ${start.y} Q ${control.x} ${control.y} ${end.x} ${end.y}`;
    const arrowheadPoints = getArrowheadPoints(end, control, arrowheadLength, arrowheadWidth);

    return (
        <svg width="100%" height="60" viewBox="0 0 100 60">
            <path d={bodyPathData} fill={arrowSettings.color} />
            <polygon points={arrowheadPoints} fill={arrowSettings.color} />
            {isAnimated && (
                <circle r={animatingCircleRadius} fill={animatingCircleColor} stroke="white" strokeWidth="1">
                    <animateMotion dur={`${animationDuration}s`} repeatCount="indefinite" path={animationPathData} />
                </circle>
            )}
        </svg>
    )
  }

  const handleTrashDrop = (e: React.DragEvent, listType: 'unit' | 'other') => {
    e.preventDefault();
    if (listType === 'unit') setIsUnitTrashHovered(false);
    else setIsOtherTrashHovered(false);
    
    const data = e.dataTransfer.getData('application/json');
    if (!data) return;

    try {
        const parsedData = JSON.parse(data);
        if (parsedData.isDeletable && parsedData.id !== undefined) {
            if (parsedData.listType === 'unit' && listType === 'unit') {
                onDeleteCustomUnit(parsedData.id);
            } else if (parsedData.listType === 'other' && listType === 'other') {
                onDeleteCustomOther(parsedData.id);
            }
        }
    } catch (err) {
        console.error("Failed to parse dropped data for deletion:", err);
    }
  };

  const handleTrashDragOver = (e: React.DragEvent, listType: 'unit' | 'other') => {
    e.preventDefault();
    if (listType === 'unit') setIsUnitTrashHovered(true);
    else setIsOtherTrashHovered(true);
  };

  if (isToolbarCollapsed) {
    return (
        <button
            className="fixed top-4 left-4 z-30 bg-gray-800/80 backdrop-blur-md text-white p-3 rounded-lg shadow-2xl flex items-center space-x-3 cursor-pointer transition-all hover:bg-gray-700/90"
            onClick={() => setIsToolbarCollapsed(false)}
            title={t('toolbar_expand')}
        >
            <div className="flex flex-col items-start space-y-2">
                <h1 className="text-lg font-bold text-blue-400 whitespace-nowrap">{t('app_title').replace('\n', ' ')}</h1>
                <div onClick={(e) => { e.stopPropagation(); onLanguageChange(); }} className="w-8 h-6 rounded-sm overflow-hidden ring-1 ring-gray-500 hover:ring-white transition-all">
                    {language === 'en' ? <UsaFlagIcon /> : <PeruFlagIcon />}
                </div>
            </div>
            <div className="text-gray-400 w-6 h-6 self-center">
                <ChevronRightIcon />
            </div>
        </button>
    );
  }

  return (
    <aside className="w-64 bg-gray-800 text-white p-4 flex flex-col h-full shadow-2xl flex-shrink-0">
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <div className="flex items-center">
            <h1 className="text-2xl font-bold text-center text-blue-400 mr-3 whitespace-pre-line">{t('app_title')}</h1>
            <button onClick={onLanguageChange} className="w-8 h-6 rounded-sm overflow-hidden ring-1 ring-gray-500 hover:ring-white transition-all">
              {language === 'en' ? <UsaFlagIcon /> : <PeruFlagIcon />}
            </button>
        </div>
        <button
            onClick={() => setIsToolbarCollapsed(true)}
            title={t('toolbar_collapse')}
            className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-white/10 transition-colors"
        >
            <div className="w-6 h-6">
                <ChevronLeftIcon />
            </div>
        </button>
      </div>
      
      <div className="flex-1 overflow-y-auto min-h-0 flex flex-col custom-scrollbar">
          <input
            type="color"
            ref={colorInputRef}
            className="hidden"
            onChange={handleColorInputChange}
            onBlur={handleCancelColorChange}
          />
          
          <div className="border-t border-gray-700 pt-4">
            <div className="flex items-center justify-between mb-2 px-2">
              <h2 className="text-sm font-semibold text-gray-400">{t('tools_title')}</h2>
              <button
                onClick={() => setIsToolsCollapsed(prev => !prev)}
                title={isToolsCollapsed ? t('toolbar_expand_tools') : t('toolbar_collapse_tools')}
                className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-white/10 transition-colors"
              >
                <div className="w-5 h-5">
                  {isToolsCollapsed ? <ChevronLeftIcon /> : <ChevronDownIcon />}
                </div>
              </button>
            </div>
            <div className={`transition-all duration-300 ease-in-out overflow-hidden ${isToolsCollapsed ? 'max-h-0' : 'max-h-[600px]'}`}>
              <ToolButton label={t('tool_select')} isActive={activeTool === 'select'} onClick={() => onToolSelect('select')}><SelectIcon /></ToolButton>
              <ToolButton label={t('tool_move')} isActive={activeTool === 'move'} onClick={() => onToolSelect('move')}><MoveIcon /></ToolButton>
              <ToolButton label={t('tool_clone')} isActive={activeTool === 'clone'} onClick={() => onToolSelect('clone')}><CloneIcon /></ToolButton>
              <ToolButton label={t('tool_zoom')} isActive={activeTool === 'zoom'} onClick={() => onToolSelect('zoom')}><ZoomIcon /></ToolButton>
              <ToolButton label={t('tool_enlarge')} isActive={activeTool === 'enlarge'} onClick={() => onToolSelect('enlarge')}><EnlargeIcon /></ToolButton>
              <ToolButton label={t('tool_text')} isActive={activeTool === 'text'} onClick={() => onToolSelect('text')}><TextIcon /></ToolButton>
              <ToolButton label={t('tool_eraser')} isActive={activeTool === 'eraser'} onClick={() => onToolSelect('eraser')}><EraserIcon /></ToolButton>
              <ToolButton label={t('tool_path')} isActive={activeTool === 'path'} onClick={() => onToolSelect('path')}><PathIcon /></ToolButton>
              <ToolButton label={t('tool_arrow')} isActive={activeTool === 'arrow'} onClick={() => onToolSelect('arrow')}><ArrowIcon /></ToolButton>
              <ToolButton label={t('tool_circle')} isActive={activeTool === 'circle'} onClick={() => onToolSelect('circle')}><CircleIcon /></ToolButton>
            </div>
          </div>

          {activeTool === 'arrow' && (
            <div className="mt-4 p-3 bg-gray-700 rounded-lg space-y-4">
              <h3 className="text-sm font-semibold text-gray-300">{t('arrow_settings_title')}</h3>
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">{t('arrow_color_label')}</label>
                <input 
                  type="color" 
                  value={arrowSettings.color} 
                  onChange={e => onArrowSettingsChange({ ...arrowSettings, color: e.target.value })} 
                  className="w-full h-8 p-0 bg-transparent border-0 rounded cursor-pointer" 
                  title={t('arrow_color_label')} />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">{t('arrow_start_width')}</label>
                <div className="flex items-center space-x-2">
                  <input type="range" min="0.5" max="30" step="0.5" value={arrowSettings.strokeWidthStart} onChange={e => onArrowSettingsChange({ ...arrowSettings, strokeWidthStart: parseFloat(e.target.value) })} className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500" />
                  <span className="text-sm font-mono text-gray-300 w-12 text-right">{arrowSettings.strokeWidthStart.toFixed(1)}px</span>
                </div>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">{t('arrow_end_width')}</label>
                <div className="flex items-center space-x-2">
                  <input type="range" min="0.5" max="30" step="0.5" value={arrowSettings.strokeWidthEnd} onChange={e => onArrowSettingsChange({ ...arrowSettings, strokeWidthEnd: parseFloat(e.target.value) })} className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500" />
                  <span className="text-sm font-mono text-gray-300 w-12 text-right">{arrowSettings.strokeWidthEnd.toFixed(1)}px</span>
                </div>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">{t('arrowhead_length')}</label>
                <div className="flex items-center space-x-2">
                  <input type="range" min="1" max="40" step="1" value={arrowSettings.arrowheadLength} onChange={e => onArrowSettingsChange({ ...arrowSettings, arrowheadLength: parseFloat(e.target.value) })} className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500" />
                  <span className="text-sm font-mono text-gray-300 w-12 text-right">{arrowSettings.arrowheadLength.toFixed(0)}px</span>
                </div>
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">{t('arrowhead_width')}</label>
                <div className="flex items-center space-x-2">
                  <input type="range" min="1" max="40" step="1" value={arrowSettings.arrowheadWidth} onChange={e => onArrowSettingsChange({ ...arrowSettings, arrowheadWidth: parseFloat(e.target.value) })} className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500" />
                  <span className="text-sm font-mono text-gray-300 w-12 text-right">{arrowSettings.arrowheadWidth.toFixed(0)}px</span>
                </div>
              </div>
              
              <div className="border-t border-gray-600 pt-3 space-y-3">
                 <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={arrowSettings.isAnimated}
                      onChange={(e) => onArrowSettingsChange({ ...arrowSettings, isAnimated: e.target.checked })}
                      className="w-4 h-4 text-blue-500 bg-gray-600 border-gray-500 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                    />
                    <span className="text-sm font-medium text-gray-300">{t('animate_circle_title')}</span>
                  </label>

                  {arrowSettings.isAnimated && (
                    <div className='space-y-4'>
                        <div>
                          <label className="block text-xs font-medium text-gray-300 mb-1">{t('circle_size')}</label>
                          <div className="flex items-center space-x-2">
                            <input type="range" min="1" max="15" step="1" value={arrowSettings.animatingCircleRadius} onChange={e => onArrowSettingsChange({ ...arrowSettings, animatingCircleRadius: parseFloat(e.target.value) })} className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500" />
                            <span className="text-sm font-mono text-gray-300 w-12 text-right">{arrowSettings.animatingCircleRadius.toFixed(0)}px</span>
                          </div>
                        </div>
                         <div>
                          <label className="block text-xs font-medium text-gray-300 mb-1">{t('circle_color')}</label>
                          <input type="color" value={arrowSettings.animatingCircleColor} onChange={e => onArrowSettingsChange({ ...arrowSettings, animatingCircleColor: e.target.value })} className="w-full h-8 p-0 bg-transparent border-0 rounded cursor-pointer" title={t('circle_color')} />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-300 mb-1">{t('animation_speed')}</label>
                          <div className="flex items-center space-x-2">
                            <input
                              type="range"
                              min="1"
                              max="10"
                              step="0.1"
                              value={11 - arrowSettings.animationDuration}
                              onChange={e => {
                                const newDuration = 11 - parseFloat(e.target.value);
                                onArrowSettingsChange({ ...arrowSettings, animationDuration: newDuration });
                              }}
                              className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
                            />
                            <span className="text-sm font-mono text-gray-300 w-12 text-right">{arrowSettings.animationDuration.toFixed(1)}s</span>
                          </div>
                        </div>
                    </div>
                  )}
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">{t('preview_title')}</label>
                <div className="p-2 bg-gray-900/50 rounded-md flex items-center justify-center">
                    <ArrowPreview />
                </div>
              </div>
            </div>
          )}

          {activeTool === 'text' && (
            <div className="mt-4 p-3 bg-gray-700 rounded-lg space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-semibold text-gray-300">{t('text_creator_title')}</h3>
                <div className="flex items-center space-x-1">
                  <button onClick={onAddTextPreset} title={t('save_preset')} className="p-1 text-gray-400 hover:text-white transition-colors"><BookmarkSquareIcon /></button>
                  <div className="relative">
                    <button onClick={() => setIsPresetListOpen(p => !p)} title={t('load_preset')} className="p-1 text-gray-400 hover:text-white transition-colors"><ChevronDownIcon /></button>
                    {isPresetListOpen && (
                        <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-600 rounded-md shadow-lg z-20 max-h-60 overflow-y-auto custom-scrollbar">
                            {textPresets.length > 0 ? textPresets.map(preset => (
                                <div key={preset.id} className="flex items-center justify-between text-sm text-white hover:bg-gray-600">
                                    <span onClick={() => { onLoadTextPreset(preset); setIsPresetListOpen(false); }} className="flex-1 py-1.5 pl-3 pr-2 cursor-pointer truncate">{preset.content}</span>
                                    <button 
                                      onClick={(e) => { e.stopPropagation(); onDeleteTextPreset(preset.id); }} 
                                      title={`${t('delete_preset_title')} "${preset.content}"`}
                                      className="p-1.5 text-gray-400 hover:text-red-500 flex-shrink-0 rounded-md hover:bg-gray-700"
                                    >
                                      <div className="w-3.5 h-3.5">
                                        <TrashIcon />
                                      </div>
                                    </button>
                                </div>
                            )) : <div className="p-2 text-sm text-gray-400">{t('no_saved_presets')}</div>}
                        </div>
                    )}
                  </div>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">{t('text_label')}</label>
                <input
                  type="text"
                  value={newText.content}
                  onChange={e => onNewTextChange({ ...newText, content: e.target.value })}
                  className="w-full bg-gray-800 text-white p-2 rounded-md border border-gray-600 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">{t('font_label')}</label>
                <select
                  value={newText.fontFamily}
                  onChange={e => onNewTextChange({ ...newText, fontFamily: e.target.value })}
                  className="w-full bg-gray-800 text-white p-2 rounded-md border border-gray-600 focus:ring-blue-500 focus:border-blue-500"
                >
                  {FONT_FACES.map(font => <option key={font} value={font}>{font}</option>)}
                </select>
              </div>
              <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">{t('outline_label')}</label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                        <input type="color" value={newText.outlineColor1} onChange={e => onNewTextChange({ ...newText, outlineColor1: e.target.value })} className="w-8 h-8 p-0 bg-transparent border-0 rounded cursor-pointer" title="Outline color 1" />
                        <input type="color" value={newText.outlineColor2} onChange={e => onNewTextChange({ ...newText, outlineColor2: e.target.value })} className="w-8 h-8 p-0 bg-transparent border-0 rounded cursor-pointer" title="Outline color 2" />
                        <span className="text-xs text-gray-400">{t('outline_colors_label')}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                        <input type="range" min="0.00" max="2.00" step="0.01" value={newText.outlineWidth} onChange={e => onNewTextChange({ ...newText, outlineWidth: parseFloat(e.target.value)})} className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500" />
                        <span className="text-sm font-mono text-gray-300 w-12 text-right">{newText.outlineWidth.toFixed(2)}px</span>
                    </div>
                  </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">{t('preview_title')}</label>
                <div
                  draggable
                  onDragStart={handleTextDragStart}
                  className="p-4 bg-gray-800 rounded-md cursor-grab active:cursor-grabbing border-2 border-dashed border-gray-500 flex items-center justify-center text-center"
                  style={{ minHeight: '80px' }}
                >
                  <span
                    className="font-bold whitespace-nowrap text-outline-anim"
                    style={{
                      fontSize: `${newText.size}rem`,
                      fontFamily: newText.fontFamily,
                      color: selectedColor1,
                      '--outline-color-1': newText.outlineColor1,
                      '--outline-color-2': newText.outlineColor2,
                      '--outline-width': `${newText.outlineWidth}px`,
                    } as React.CSSProperties}
                  >
                    {newText.content}
                  </span>
                </div>
                <div className="flex items-center justify-center mt-2 space-x-2">
                    <button onClick={() => handleTextSizeChange(-0.1)} className="p-1.5 bg-gray-600 rounded-full hover:bg-gray-500 transition-colors"><MinusIcon /></button>
                    <span className="text-sm font-mono text-gray-300 w-12 text-center">{newText.size.toFixed(1)}rem</span>
                    <button onClick={() => handleTextSizeChange(0.1)} className="p-1.5 bg-gray-600 rounded-full hover:bg-gray-500 transition-colors"><PlusIcon /></button>
                </div>
              </div>
            </div>
          )}

          {displayToken && (
            <div className="border-t border-gray-700 pt-4 mt-4">
              <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">{t('selected_token_title')}</h2>
              <div className="p-2 space-y-4 bg-gray-700 rounded-lg">
                
                {/* --- ROTATION --- */}
                <div>
                  <div className="flex justify-between items-center mb-1 px-1">
                    <label className="text-sm font-medium text-gray-300">{t('angle_label')}</label>
                    <span className="font-mono text-sm text-gray-300">{Math.round(displayToken.rotation ?? 0)}°</span>
                  </div>
                  <input
                    type="range"
                    min="-180"
                    max="180"
                    step="1"
                    value={displayToken.rotation ?? 0}
                    onChange={(e) => handleDisplayTokenUpdate({ rotation: parseFloat(e.target.value) })}
                    className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
                  />
                </div>
                
                {/* --- ANIMATION --- */}
                <div>
                  <div className="flex justify-between items-center mb-1 px-1">
                    <label className="text-sm font-medium text-gray-300">{t('speed_label')}</label>
                    <span className="font-mono text-sm text-gray-300">{(displayToken.animationSpeed ?? 1.0).toFixed(2)}x</span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="2"
                    step="0.01"
                    value={displayToken.animationSpeed ?? 1.0}
                    onChange={(e) => handleDisplayTokenUpdate({ animationSpeed: parseFloat(e.target.value)})}
                    className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
                  />
                </div>
                <div>
                  <label className="flex items-center space-x-3 cursor-pointer px-1">
                    <input
                      type="checkbox"
                      checked={displayToken.isFlipped ?? false}
                      onChange={(e) => handleDisplayTokenUpdate({ isFlipped: e.target.checked })}
                      className="w-4 h-4 text-blue-500 bg-gray-600 border-gray-500 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                    />
                    <span className="text-sm font-medium text-gray-300">{t('mirror_mode_label')}</span>
                  </label>
                </div>
                <div>
                  <label className="flex items-center space-x-3 cursor-pointer px-1">
                    <input
                      type="checkbox"
                      checked={displayToken.isPatrol ?? false}
                      onChange={(e) => handleDisplayTokenUpdate({ isPatrol: e.target.checked })}
                      className="w-4 h-4 text-blue-500 bg-gray-600 border-gray-500 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                    />
                    <span className="text-sm font-medium text-gray-300">{t('patrol_mode_label')}</span>
                  </label>
                </div>

                {/* --- PATH ACTIONS --- */}
                {selectedToken && (
                  <div className="pt-2">
                      <button
                          onClick={() => onClearPath(selectedToken.id)}
                          className="w-full bg-yellow-600 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded transition-colors"
                      >
                          {t('clear_path_button')}
                      </button>
                  </div>
                )}
                
                {displayToken.type !== EUnitType.Text && (
                  <div className="pt-4 border-t border-gray-600">
                    <div className="space-y-4">
                      {/* --- PREVIEW --- */}
                      <div>
                        <label className="block text-center text-sm font-medium text-gray-300 mb-2">{t('token_preview_label')}</label>
                        <div className="flex justify-center items-center h-20 bg-gray-900/50 rounded-lg p-2">
                          <div className="w-16 h-16">
                            <svg 
                              viewBox="0 0 100 100" 
                              className="w-full h-full"
                              style={{ 
                                filter: (displayToken.outlineWidth ?? 0) > 0 ? `url(#toolbar-preview-outline)` : 'none',
                                overflow: 'visible'
                              }}
                            >
                              <defs>
                                <filter id="toolbar-preview-outline" x="-50%" y="-50%" width="200%" height="200%">
                                    <feMorphology in="SourceAlpha" result="DILATED" operator="dilate" radius={displayToken.outlineWidth || 0} />
                                    <feFlood floodColor={displayToken.outlineColor1 || '#FFFFFF'} result="flood">
                                      {(displayToken.outlineColor2 && displayToken.outlineColor1 !== displayToken.outlineColor2) && (
                                          <animate 
                                              attributeName="flood-color"
                                              values={`${displayToken.outlineColor1};${displayToken.outlineColor2};${displayToken.outlineColor1}`}
                                              dur="2s"
                                              repeatCount="indefinite"
                                          />
                                      )}
                                    </feFlood>
                                    <feComposite in="flood" in2="DILATED" operator="in" result="OUTLINE" />
                                    <feMerge>
                                        <feMergeNode in="OUTLINE" />
                                        <feMergeNode in="SourceGraphic" />
                                    </feMerge>
                                </filter>
                              </defs>

                              <g transform={displayToken.isFlipped ? 'scale(-1, 1) translate(-100, 0)' : 'none'}>
                                { (displayToken.type === EUnitType.Custom || displayToken.type === EUnitType.CustomOther) && displayToken.customImage ? (
                                  <image href={displayToken.customImage} x="0" y="0" height="100" width="100" />
                                ) : (
                                  <g transform="translate(15, 15) scale(0.7, 0.7)" style={{color: displayToken.color}} className="token-icon">
                                    {displayToken.type === EUnitType.Other && displayToken.otherType ? 
                                      otherIcons[displayToken.otherType] : 
                                      unitIcons[displayToken.type]
                                    }
                                  </g>
                                )}
                              </g>

                              {displayToken.number !== null && (
                                <text
                                  x="50"
                                  y="55"
                                  textAnchor="middle"
                                  dominantBaseline="middle"
                                  stroke="black"
                                  strokeWidth="3"
                                  paintOrder="stroke"
                                  fontSize="40"
                                  fontWeight="bold"
                                  fontFamily="sans-serif"
                                  className="number-color-anim"
                                >
                                  {displayToken.number}
                                </text>
                              )}
                            </svg>
                          </div>
                        </div>
                      </div>
                      
                      {/* --- OUTLINE WIDTH --- */}
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <label className="text-sm font-medium text-gray-300">{t('outline_width_label')}</label>
                          <span className="font-mono text-sm text-gray-300 w-12 text-right">{(displayToken.outlineWidth || 0).toFixed(2)}px</span>
                        </div>
                        <input
                          type="range"
                          min="0.00"
                          max="2.00"
                          step="0.01"
                          value={displayToken.outlineWidth || 0}
                          onChange={(e) => handleDisplayTokenUpdate({ outlineWidth: parseFloat(e.target.value) })}
                          className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="border-t border-gray-700 pt-4 mt-4">
            <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">{t('global_settings_title')}</h2>
            <div className="p-2 space-y-2 bg-gray-700 rounded-lg">
                <div>
                  <div className="flex justify-between items-center mb-1 px-1">
                    <label className="text-sm font-medium text-gray-300">{t('all_tokens_size_label')}</label>
                    <span className="font-mono text-sm text-gray-300">{globalTokenSize}%</span>
                  </div>
                  <input
                    type="range"
                    min="-100"
                    max="100"
                    step="1"
                    value={globalTokenSize}
                    onChange={(e) => onGlobalTokenSizeChange(parseInt(e.target.value, 10))}
                    className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
                  />
                </div>
                <div>
                  <label className="flex items-center space-x-3 cursor-pointer px-1 py-1">
                    <input
                      type="checkbox"
                      checked={arePathsVisible}
                      onChange={(e) => onArePathsVisibleChange(e.target.checked)}
                      className="w-4 h-4 text-blue-500 bg-gray-600 border-gray-500 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                    />
                    <span className="text-sm font-medium text-gray-300">{t('show_paths_label')}</span>
                  </label>
                </div>
            </div>
          </div>

          <div className="border-t border-gray-700 pt-4 mt-4">
            <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">{t('color_title')}</h2>
            <div className="flex items-center justify-center space-x-4 mb-3 p-1">
              <div className="text-center">
                <button
                    onClick={() => setActiveSwatch(1)}
                    className={`w-10 h-10 rounded-lg transition-all duration-200 ${activeSwatch === 1 ? 'ring-2 ring-offset-2 ring-offset-gray-800 ring-white' : 'ring-1 ring-gray-600'}`}
                    style={{ backgroundColor: selectedColor1 }}
                    title={`${t('color_1_label')}: ${selectedColor1}`}
                />
                <span className="text-xs text-gray-400 mt-1 block">{t('color_1_label')}</span>
              </div>
              <div className="text-center">
                <button
                    onClick={() => setActiveSwatch(2)}
                    className={`w-10 h-10 rounded-lg transition-all duration-200 ${activeSwatch === 2 ? 'ring-2 ring-offset-2 ring-offset-gray-800 ring-white' : 'ring-1 ring-gray-600'}`}
                    style={{ backgroundColor: selectedColor2 }}
                    title={`${t('color_2_label')}: ${selectedColor2}`}
                />
                <span className="text-xs text-gray-400 mt-1 block">{t('color_2_label')}</span>
              </div>
            </div>
            <div className="grid grid-cols-7 gap-1.5 p-1">
                {paletteColors.map((color, index) => {
                    const isSelected = selectedColor1 === color || selectedColor2 === color;
                    return (
                      <button
                          key={`${color}-${index}`}
                          title={`${color} (${t('color_change_prompt')})`}
                          onClick={() => (activeSwatch === 1 ? onColor1Change(color) : onColor2Change(color))}
                          onDoubleClick={() => handleColorDoubleClick(index)}
                          className={`w-7 h-7 rounded-full transition-transform duration-150 ${isSelected ? 'ring-2 ring-offset-2 ring-offset-gray-800 ring-white' : 'ring-1 ring-transparent hover:ring-white'}`}
                          style={{ backgroundColor: color }}
                      />
                    )
                })}
            </div>
            {pendingColorChange && (
                <div className="mt-2 p-2 bg-gray-700 rounded-lg flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <span className="text-sm">{t('new_color_label')}</span>
                        <div className="w-5 h-5 rounded-full ring-1 ring-white" style={{ backgroundColor: pendingColorChange.color }}></div>
                    </div>
                    <div className="flex gap-2">
                        <button onClick={handleConfirmColorChange} className="bg-blue-600 hover:bg-blue-500 text-white px-3 py-1 text-sm rounded">{t('confirm_button')}</button>
                        <button onClick={handleCancelColorChange} className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-1 text-sm rounded">{t('cancel_button')}</button>
                    </div>
                </div>
            )}
          </div>

          <div className="border-t border-gray-700 pt-4 mt-4">
            <div className="flex items-center justify-between mb-2 px-2">
              <h2 className="text-sm font-semibold text-gray-400">{t('units_title')}</h2>
              <label title={t('upload_custom_unit_title')} className="p-1.5 text-gray-400 hover:text-white rounded-md hover:bg-gray-700 cursor-pointer transition-colors">
                <div className="w-5 h-5"><UploadIcon /></div>
                <input type="file" className="hidden" accept="image/png,image/gif" multiple onChange={onCustomUnitUpload} />
              </label>
            </div>
            <div className="grid grid-cols-3 gap-2">
                {UNIT_TYPES.map(type => (
                  <div
                    key={type}
                    title={t(`unit_${type.toLowerCase()}`)}
                    draggable
                    onDragStart={(e) => handleUnitDragStart(e, type)}
                    onClick={() => handleUnitPreview(type)}
                    className="p-2 rounded-lg cursor-grab active:cursor-grabbing flex justify-center items-center bg-gray-700 hover:bg-gray-600 transition-colors"
                  >
                    <div className="w-8 h-8" style={{ color: selectedColor1 }}>{unitIcons[type]}</div>
                  </div>
                ))}
                {customUnits.map(unit => (
                  <div
                    key={unit.id}
                    title={unit.name}
                    draggable
                    onDragStart={(e) => handleCustomUnitDragStart(e, unit)}
                    onClick={() => handleUnitPreview(EUnitType.Custom, { customImage: unit.imageData, name: unit.name })}
                    className="p-2 rounded-lg cursor-grab active:cursor-grabbing flex justify-center items-center bg-gray-700 hover:bg-gray-600 transition-colors"
                  >
                    <img src={unit.imageData} alt={unit.name} className="w-8 h-8 object-contain" />
                  </div>
                ))}
                {customUnits.length > 0 && (
                  <div
                    title={t('drag_to_delete_title')}
                    onDrop={(e) => handleTrashDrop(e, 'unit')}
                    onDragOver={(e) => handleTrashDragOver(e, 'unit')}
                    onDragLeave={() => setIsUnitTrashHovered(false)}
                    className={`p-2 rounded-lg flex justify-center items-center transition-colors ${isUnitTrashHovered ? 'bg-red-800 ring-2 ring-red-500' : 'bg-gray-700'}`}
                  >
                    <div className="w-8 h-8 text-red-500"><TrashIcon /></div>
                  </div>
                )}
            </div>
          </div>

          <div className="border-t border-gray-700 pt-4 mt-4">
            <div className="flex items-center justify-between mb-2 px-2">
              <h2 className="text-sm font-semibold text-gray-400">{t('others_title')}</h2>
              <label title={t('upload_custom_other_title')} className="p-1.5 text-gray-400 hover:text-white rounded-md hover:bg-gray-700 cursor-pointer transition-colors">
                <div className="w-5 h-5"><UploadIcon /></div>
                <input type="file" className="hidden" accept="image/png,image/gif" multiple onChange={onCustomOtherUpload} />
              </label>
            </div>
              <div className="grid grid-cols-3 gap-2">
                {OTHER_TYPES.map(unit => (
                  <div
                    key={unit.name}
                    title={t(`other_${unit.type.toLowerCase().replace(/ /g, '_')}`)}
                    draggable
                    onDragStart={(e) => handleOtherDragStart(e, unit.type)}
                    onClick={() => handleUnitPreview(EUnitType.Other, { otherType: unit.type })}
                    className="p-2 rounded-lg cursor-grab active:cursor-grabbing flex justify-center items-center bg-gray-700 hover:bg-gray-600 transition-colors"
                  >
                    <div className="w-8 h-8" style={{ color: selectedColor1 }}>{otherIcons[unit.type]}</div>
                  </div>
                ))}
                {customOthers.map(unit => (
                  <div
                    key={unit.id}
                    title={unit.name}
                    draggable
                    onDragStart={(e) => handleCustomOtherDragStart(e, unit)}
                    onClick={() => handleUnitPreview(EUnitType.CustomOther, { customImage: unit.imageData, name: unit.name })}
                    className="p-2 rounded-lg cursor-grab active:cursor-grabbing flex justify-center items-center bg-gray-700 hover:bg-gray-600 transition-colors"
                  >
                    <img src={unit.imageData} alt={unit.name} className="w-8 h-8 object-contain" />
                  </div>
                ))}
                {customOthers.length > 0 && (
                  <div
                    title={t('drag_to_delete_title')}
                    onDrop={(e) => handleTrashDrop(e, 'other')}
                    onDragOver={(e) => handleTrashDragOver(e, 'other')}
                    onDragLeave={() => setIsOtherTrashHovered(false)}
                    className={`p-2 rounded-lg flex justify-center items-center transition-colors ${isOtherTrashHovered ? 'bg-red-800 ring-2 ring-red-500' : 'bg-gray-700'}`}
                  >
                    <div className="w-8 h-8 text-red-500"><TrashIcon /></div>
                  </div>
                )}
              </div>
          </div>
          
          <div className="border-t border-gray-700 pt-4 mt-4">
            <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">{t('history_title')}</h2>
            <div className="flex items-center space-x-2 px-1">
                <button
                    onClick={onUndo}
                    title={t('undo_title')}
                    className="w-1/2 flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-gray-300 p-2 rounded-lg transition-colors duration-200"
                >
                    <div className="w-5 h-5"><UndoIcon /></div>
                    <span className="ml-2 font-medium text-sm">{t('undo_button')}</span>
                </button>
                <button
                    onClick={onRedo}
                    title={t('redo_title')}
                    className="w-1/2 flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-gray-300 p-2 rounded-lg transition-colors duration-200"
                >
                    <div className="w-5 h-5"><RedoIcon /></div>
                    <span className="ml-2 font-medium text-sm">{t('redo_button')}</span>
                </button>
            </div>
          </div>

          <div className="border-t border-gray-700 pt-4 mt-auto">
             <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">{t('import_export_title')}</h2>
             <div className="flex items-center space-x-2 px-1">
                <label title={t('import_title')} className="w-1/2 cursor-pointer">
                    <div className="flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-gray-300 p-2 rounded-lg transition-colors duration-200">
                        <div className="w-5 h-5"><ImportIcon /></div>
                        <span className="ml-2 font-medium text-sm">{t('import_button')}</span>
                        <input type="file" className="hidden" accept=".json" onChange={onImportProject} />
                    </div>
                </label>
                <button
                    onClick={onExportProject}
                    title={t('export_title')}
                    className="w-1/2 flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-gray-300 p-2 rounded-lg transition-colors duration-200"
                >
                    <div className="w-5 h-5"><ExportIcon /></div>
                    <span className="ml-2 font-medium text-sm">{t('export_button')}</span>
                </button>
            </div>
             <div className="flex items-center space-x-2 px-1 mt-2">
                <label title={t('upload_map_title')} className="w-1/2 cursor-pointer">
                    <div className="flex items-center justify-center bg-blue-700 hover:bg-blue-600 text-white p-2 rounded-lg transition-colors duration-200">
                        <div className="w-5 h-5"><UploadIcon /></div>
                        <span className="ml-2 font-medium text-sm">{t('map_button')}</span>
                        <input type="file" className="hidden" accept="image/*,video/mp4" onChange={onMapUpload} />
                    </div>
                </label>
                <button
                    onClick={onClearAll}
                    title={t('clear_all_title')}
                    className="w-1/2 flex items-center justify-center bg-red-700 hover:bg-red-600 text-white p-2 rounded-lg transition-colors duration-200"
                >
                    <div className="w-5 h-5"><TrashIcon /></div>
                    <span className="ml-2 font-medium text-sm">{t('clear_button')}</span>
                </button>
            </div>
          </div>
      </div>
    </aside>
  );
};