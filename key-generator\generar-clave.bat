@echo off
chcp 65001 >nul
title Generador de Claves - Strategy Creator 2.2

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🔑 GENERADOR DE CLAVES DE ACTIVACIÓN            ║
echo ║                    Strategy Creator 2.2                      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MENU
echo Seleccione una opción:
echo.
echo [1] Generar nueva clave de activación
echo [2] Validar clave existente
echo [3] Salir
echo.
set /p "opcion=Ingrese su opción (1-3): "

if "%opcion%"=="1" goto GENERAR
if "%opcion%"=="2" goto VALIDAR
if "%opcion%"=="3" goto SALIR
echo.
echo ❌ Opción inválida. Intente nuevamente.
echo.
goto MENU

:GENERAR
echo.
echo ═══════════════════════════════════════════════════════════════
echo                    GENERAR NUEVA CLAVE
echo ═══════════════════════════════════════════════════════════════
echo.
echo Ingrese la dirección MAC del equipo de destino.
echo Formato: XX:XX:XX:XX:XX:XX (ejemplo: 00:1B:44:11:3A:B7)
echo.
set /p "mac=MAC Address: "

if "%mac%"=="" (
    echo ❌ MAC Address requerida.
    echo.
    pause
    goto MENU
)

echo.
echo Generando clave para MAC: %mac%
echo.
node generate-key.cjs "%mac%"
echo.
echo ═══════════════════════════════════════════════════════════════
pause
goto MENU

:VALIDAR
echo.
echo ═══════════════════════════════════════════════════════════════
echo                     VALIDAR CLAVE
echo ═══════════════════════════════════════════════════════════════
echo.
set /p "mac=MAC Address: "
set /p "clave=Clave de Activación: "

if "%mac%"=="" (
    echo ❌ MAC Address requerida.
    echo.
    pause
    goto MENU
)

if "%clave%"=="" (
    echo ❌ Clave de activación requerida.
    echo.
    pause
    goto MENU
)

echo.
echo Validando clave...
echo.
node generate-key.cjs "%mac%" "%clave%"
echo.
echo ═══════════════════════════════════════════════════════════════
pause
goto MENU

:SALIR
echo.
echo ¡Gracias por usar el Generador de Claves!
echo.
pause
exit
